﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="imageList_TestStatus.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>38, 19</value>
  </metadata>
  <data name="imageList_TestStatus.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAO
        HQAAAk1TRnQBSQFMAgEBAwEAAfABEAHwARABGQEAARkBAAT/ASEBAAj/AUIBTQE2BwABNgMAASgDAAFk
        AwABGQMAAQEBAAEgBQABEAEnMgADXgHdA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DUAGd
        /wBhAANhAd4DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DRwGA
        OAADLQFGAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AxsBJlAA
        AwgBCgMGAQekAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8sAANYAbcBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wM7AWU4AAMcAScCAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8BRwFBAWoB+QMEAQWQAAOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/yUA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AVEBbQFRAfcwAAJAAagB/QIA
        AdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wNd
        AeqIAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/HQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8qAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf+AAANg
        AfMDigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wNGAX8VAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AVEBbQFRAfciAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf94AAMHAQkDigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        EAADYQHmAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AzsBZBoAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIA
        AdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIA
        AdYB/wIAAdYB/wIAAdYB/wNhAet0AAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A1ABnQ0A
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8UAAM1AVYCAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8DSgGKAwQBBQIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wYAAdYB/wIA
        AdYB/wIAAdYB/wIAAdYB/wIAAdYB/wMEAQVwAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wgAA1YBsAEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8EAANaAfUBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wMbASUSAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8IAAMGAQcCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8OAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8BRwFBAWoB+XAAA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/CQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wwA
        A2EB6wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/EgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        CAADCAEKAgAB1gH/AgAB1gH/DgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/bAADQAFw
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A2EB6wNcAcMDigH/A4oB/wOKAf8DXAHLA14B3QOKAf8DigH/
        A4oB/wNWAbUDigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/CQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8IAANYAbgIAANhAeYBAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8MAAMb
        ASYCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CQAGoAf0IAAMJAQwOAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf9sAANdAd8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8IAAMJAQwDigH/Az4BaggAA10B3wOKAf8MAAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wkAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wQABAIBAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8DEgEXBAABWwFhAVsB5AEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8MAAMzAVICAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8DTQH6EgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        AgAB1gH/AwYBB2gAA10B6gOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wgAAwYBBwOKAf8DKgE/CAADXAHL
        A4oB/wwAA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/CQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wQAAUwBTQFMAZEBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        A0oBigQAA2AB6AEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/DAADOAFbAgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        DAADCgENAgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AwcBCWgA
        A08BmQOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wNNAZMDHgEqA4oB/wOKAf8DigH/AygBPAM+AWsDigH/
        A4oB/wOKAf8DCwEOA2IB4QOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wkAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/BAADKwH8AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8DXgHwBAADYQHrAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/DAADIAEuAgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        FAADCAEKAgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/cAADigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8JAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8EAAFcAWUBXAHnAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wwABAECAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8OAAHW
        Af8DTQH6CAADBgEIAgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/cAADigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8IAAFdAWUBXQHsAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/Ax0BKQFbAWEBWwHkAQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/Ay0BRhIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/w4AAdYB/wIAAdYB/wIA
        AdYB/wJAAagB/QgAAwUBBgIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/3AAA4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DYQHcCAADBgEHAQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/xQAA1IBoAIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wMJAQsGAAHWAf8CAAHWAf8CAAHW
        Af8CAAHWAf8CAAHWAf8CAAHWAf8EAANKAYoCAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8DGwEmcAADEAEV
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/xAAAUABkwFAAf0BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8DVgG2GgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AkABqAH9eAADigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DYQHeFQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/HAADFwEfAgAB1gH/
        AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/gAADigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/xgAAxMBGQEA
        AZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEA
        AZMBAAH/JAADOAFbAgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/
        AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/AgAB1gH/hAADEwEZA4oB/wOKAf8DigH/A4oB/wOK
        Af8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/IAADGAEh
        AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGT
        AQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/ywAAxcBHwIA
        AdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIAAdYB/wIA
        AdYB/wIAAdYB/4wAAxgBIAOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8sAAFAAZMBQAH9AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEA
        Af8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8DYQHmOAACUAFR
        AZ8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8CAAHWAf8DNQFWmAADigH/
        A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A2AB8zQAAwYBBwNh
        AesBAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BAAGTAQAB/wEAAZMBAAH/AQABkwEAAf8BVQFXAVUBsUgA
        BAEDIAEuAzgBWwMzAVIDGwEmqAADEAEVA4oB/wOKAf8DigH/A4oB/wOKAf8DigH/A4oB/wOKAf8DigH/
        A4oB/wMHAQn/AHEAA08BmQNdAeoDXQHfA0ABcP8AVQABQgFNAT4HAAE+AwABKAMAAWQDAAEZAwABAQEA
        AQEFAAGQAQEWAAP/AQAB/wEAAT8G/wHgBgAB/AEAAQ8B/wHAAT8B/wH8Af8B4AYAAfgBAAEHAf8BAAEP
        Af8BwAEPAeAGAAHwAQABAwH+AQABBwH/AYABBwHgBgAB4AEAAQEB/AEAAQMB/wEAAQMB4AYAAcACAAH4
        AQABAQH+AQABAQHgBgABgAIAAfACAAH8AgAB4AYAAYACAAFwAgAB+AEAASABYAYAAYACAAFgAQgBAAF4
        ATABcAFgBgABgAIAAWABHAEAAXgBGAHgAWAJAAFgATYBAAFwAQ0BwAFgBgABAQGMAeABYAFBAQABcAEH
        AYABIAYAAQEBjAHgAWACgAFwAQcBAAEgCQABYQEAAUABcAEPAYABYAYAAYACAAFgAQABIAFwARwBwAFg
        BgABgAIAAWACAAF4ATgCYAYAAYACAAFgAgAB+AEQASABYAYAAYACAAHwAgAB/AIAAeAGAAHAAgAB+AEA
        AQEB/AEAAQEB4AYAAeABAAEBAfgBAAEDAf4BAAEDAeAGAAHgAQABAwH8AQABBwH/AQABBwHgBgAB8AEA
        AQcB/wEAAQ8B/wHAAQ8B4AYAAfwBAAEPAf8BgAE/Af8B8AF/AeAGAAH+AQABHwb/AeAGAAH/AeEH/wHg
        BgAL
</value>
  </data>
  <metadata name="timer_ShowTime.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>221, 25</value>
  </metadata>
</root>