{"Project": "GD10_PCBA", "Version": "2025-05-20 10:25:59", "UpdateTime": "2025-05-20 10:26:09", "Data": [{"Name": "校验ID(SN)规则", "CmdStr": "", "AsciiSend": true, "EnterLine": true, "ClearData": false, "EndWithStr": "", "WaitTime": 1000, "WaitCount": 30, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "校验ID(SN)规则", "IsEnabled": true, "RegexPattern": "T6G613\\d{10}", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "", "CompareTextType": 3, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "等待设备连接", "CmdStr": "", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "fix_connect", "WaitTime": 1000, "WaitCount": 30, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "等待设备连接", "IsEnabled": true, "RegexPattern": "fix_connect", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "fix_connect", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "上电获取设备信息", "CmdStr": "6A A6 01 6A A6", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "Slave HMT example started", "WaitTime": 1000, "WaitCount": 30, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "读取蓝牙地址", "IsEnabled": true, "RegexPattern": "Local Board.*", "CutTextType": 3, "CutTextFlag1": "Local Board", "CutTextFlag2": ".", "IsCompareText": true, "TargetValue": "", "CompareTextType": 3, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "1.8V（LDO输出-关）", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "1.8V（LDO输出-关）", "IsEnabled": true, "RegexPattern": "Get AD_V1 .*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V1", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 100.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "1.8V(电容供电-关)", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "1.8V(电容供电-关)", "IsEnabled": true, "RegexPattern": "Get AD_V2 .*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 100.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "1.8V(红外供电-关)", "CmdStr": "1A A1 00 00 08", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "1.8V(红外供电-关)", "IsEnabled": true, "RegexPattern": "Get AD_V3 .*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V3", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 100.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "3V（三轴供电）", "CmdStr": "1A A1 00 00 10", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "3V（三轴供电）", "IsEnabled": true, "RegexPattern": "Get AD_V4 .*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V4", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 2.0, "MinValue": 2900.0, "MaxValue": 3100.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "切换小量程模式", "CmdStr": "6A A6 02 6A A6", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "切换小量程模式", "IsEnabled": true, "RegexPattern": "Battery 2ON", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "Battery 2ON", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 100.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "广播电流", "CmdStr": "7A A7 01 7A A7", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "广播电流", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 60.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "蓝牙测试", "CmdStr": "<SPBSJ*MAC:[mac]>", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "adv_info reload fc", "WaitTime": 1000, "WaitCount": 10, "Retry": 2, "SleepTime": 0, "Remark": "", "Params": [{"Name": "蓝牙连接", "IsEnabled": true, "RegexPattern": "adv_info reload fc", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "adv_info reload fc", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "蓝牙RSSI", "IsEnabled": true, "RegexPattern": "RSSI.*ADD", "CutTextType": 3, "CutTextFlag1": "RSSI:", "CutTextFlag2": "*ADD", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": -50.0, "MaxValue": -10.0, "Unit": "DB", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "1.8V（LDO输出-开）", "CmdStr": "1A A1 00 00 02", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "1.8V（LDO输出-开）", "IsEnabled": true, "RegexPattern": "Get AD_V1 .*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V1", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1700.0, "MaxValue": 1900.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "1.8V(电容供电-开)", "CmdStr": "1A A1 00 00 04", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "1.8V(电容供电-开)", "IsEnabled": true, "RegexPattern": "Get AD_V2 .*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V2", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1700.0, "MaxValue": 1900.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "1.8V(红外供电-开)", "CmdStr": "1A A1 00 00 08", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 4, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "1.8V(红外供电-开)", "IsEnabled": true, "RegexPattern": "Get AD_V3 .*mV", "CutTextType": 3, "CutTextFlag1": "Get AD_V3", "CutTextFlag2": "mV", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1700.0, "MaxValue": 1900.0, "Unit": "mV", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "工作电流", "CmdStr": "7A A7 01 7A A7", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "OVER 150", "WaitTime": 250, "WaitCount": 8, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "工作电流", "IsEnabled": true, "RegexPattern": "ACK.CURRENT.*uA", "CutTextType": 3, "CutTextFlag1": "ACK+CURRENT:", "CutTextFlag2": "uA", "IsCompareText": false, "TargetValue": "", "CompareTextType": 1, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 200.0, "Unit": "uA", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "蓝牙断开", "CmdStr": "4A A4 04 4A A4", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "disconnect", "WaitTime": 1000, "WaitCount": 5, "Retry": 2, "SleepTime": 0, "Remark": "", "Params": [{"Name": "蓝牙断开", "IsEnabled": true, "RegexPattern": "disconnect", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "disconnect", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "MES过站", "CmdStr": "", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "", "WaitTime": 250, "WaitCount": 4, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "MES过站", "IsEnabled": false, "RegexPattern": "^[A-Za-z]+", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "True", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 10.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}]}