2025-07-05 11:27:05:665
扫码结果:T6G6132521000023D93E1635CAF7
开始测试T6G6132521000023
当前测试项目:GD10_Finish
测试参数版本:2.0.0.0
参数更新时间:2025-06-12 10:35:08


▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:05:676】开始检测【1.扫描设备RSSI】
向【COM5】发送指令:【AT+SCAN?】
Scanning...
0: 0xCD091435BEF9, -60, MHG5
1: 0xCD681435F3F9, -51, MHG5
2: 0xD93E1635CAF7, -56, MHG5
3: 0xE20916358EF5, -47, MHG5
4: 0xD6561435BCF9, -43, MHG5
5: 0xD67B1435DFF9, -38, MHG5
6: 0x3DE768750B4D, -62
7: 0x24F1503DF65B, -65, ROADBIT
8: 0x1BF789A57833, -64
Devices Found: 9
【扫描设备RSSI】通过,【-56】符合目标值范围【-70】至【-10】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:11:684】开始检测【2.连接蓝牙设备(1)】
向【COM5】发送指令:【AT+COND93E1635CAF7】
Connecting
Pairing started
Pairing started
OK+CONN:0xD93E1635CAF7
Pairing success
【连接蓝牙设备(1)】通过,返回值【Pairing success】与目标值【Pairing success】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:12:707】开始检测【3.关闭日志打印】
向【COM5】发送指令:【AT+SETLOG=0】
0: 2A00, Read
1: 2A01, Read
2: 2AA6, Read
3: 2AC9, Read
4: 2A05, Indicate
5: A6ED0202-D344-460A-8075-B9E8EC90D71B, Notify; RX
6: A6ED0203-D344-460A-8075-B9E8EC90D71B, Write no rsp, Write; TX
7: A6ED0204-D344-460A-8075-B9E8EC90D71B, Write, Notify
8: A6ED0702-D344-460A-8075-B9E8EC90D71B, Write no rsp, Notify
9: A6ED0703-D344-460A-8075-B9E8EC90D71B, Write no rsp
Chars Found: 10
U?log level 0
reg[0x0978]=0x1CDE(7390),reg[0x097A]=0x1CDE(7390),s0=429879,s1=193419,s0-s1=236460
【关闭日志打印】通过,返回值【log level 0】与目标值【log level 0】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:13:714】开始检测【4.打开电容读取】
向【COM5】发送指令:【AT+SETLOG=11】
log level 11
reg[0x0978]=0x1CE0(7392),reg[0x097A]=0x1CE0(7392),s0=429801,s1=193275,s0-s1=236526
reg[0x0978]=0x1CDF(7391),reg[0x097A]=0x1CDF(7391),s0=429798,s1=193309,s0-s1=236489
reg[0x0978]=0x1CE0(7392),reg[0x097A]=0x1CE0(7392),s0=429824,s1=193317,s0-s1=236507
reg[0x0978]=0x1CDF(7391),reg[0x097A]=0x1CDF(7391),s0=429795,s1=193323,s0-s1=236472
【打开电容读取】通过,返回值【log level 11】与目标值【log level 11】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:14:730】开始检测【5.校验BootVer】
向【COM5】发送指令:【AT+INFO】
bootver:102,hardver:102,appver:928,mac:D9:3E:16:35:CA:F7reg[0x0978]=0x1CDE(7390),reg[0x097A]=0x1CDE(7390),s0=429783,s1=193287,s0-s1=236496
reg[0x0978]=0x1CDE(7390),reg[0x097A]=0x1CDE(7390),s0=429742,s1=193293,s0-s1=236449
U?reg[0x0978]=0x1CDD(7389),reg[0x097A]=0x1CDD(7389),s0=429741,s1=193287,s0-s1=236454
【校验BootVer】通过,返回值【102】与目标值【102】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:15:781】开始检测【6.校验hardver】
【校验hardver】通过,返回值【102】与目标值【102】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:15:813】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR】
bat:3352,0,ir:0,cap:0,0,0,gsenor:-183,-51,-917
reg[0x0978]=0x1CDD(7389),reg[0x097A]=0x1CDD(7389),s0=429693,s1=193274,s0-s1=236419
reg[0x0978]=0x1CDD(7389),reg[0x097A]=0x1CDD(7389),s0=429713,s1=193273,s0-s1=236440
reg[0x0978]=0x1CDC(7388),reg[0x097A]=0x1CDC(7388),s0=429659,s1=193287,s0-s1=236372
reg[0x0978]=0x1CDC(7388),reg[0x097A]=0x1CDC(7388),s0=429607,s1=193212,s0-s1=236395
reg[0x0978]=0x1CDB(7387),reg[0x097A]=0x1CDB(7387),s0=429592,s1=193222,s0-s1=236370
【电池1电压】通过,【3352】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:16:839】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:16:846】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR】
bat:3354,0,ir:0,cap:0,0,0,gsenor:-192,-62,-919
reg[0x0978]=0x1CDB(7387),reg[0x097A]=0x1CDB(7387),s0=429541,s1=193166,s0-s1=236375
reg[0x0978]=0x1CDB(7387),reg[0x097A]=0x1CDB(7387),s0=429544,s1=193175,s0-s1=236369
U?reg[0x0978]=0x1CDA(7386),reg[0x097A]=0x1CDA(7386),s0=429522,s1=193167,s0-s1=236355
【电池1电压】通过,【3354】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:17:851】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:17:854】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR】
reg[0x0978]=0x1CDA(7386),reg[0x097A]=0x1CDA(7386),s0=429517,s1=193126,s0-s1=236391
bat:3345,0,ir:0,cap:0,0,0,gsenor:-190,-58,-922
reg[0x0978]=0x1CDA(7386),reg[0x097A]=0x1CDA(7386),s0=429455,s1=193127,s0-s1=236328
reg[0x0978]=0x1CD9(7385),reg[0x097A]=0x1CD9(7385),s0=429429,s1=193148,s0-s1=236281
reg[0x0978]=0x1CD9(7385),reg[0x097A]=0x1CD9(7385),s0=429440,s1=193090,s0-s1=236350
reg[0x0978]=0x1CD9(7385),reg[0x097A]=0x1CD9(7385),s0=429454,s1=193136,s0-s1=236318
【电池1电压】通过,【3345】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:18:862】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:18:866】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR】
reg[0x0978]=0x1CD9(7385),reg[0x097A]=0x1CD9(7385),s0=429334,s1=193081,s0-s1=236253
bat:3353,0,ir:0,cap:0,0,0,gsenor:-182,-54,-917
reg[0x0978]=0x1CD8(7384),reg[0x097A]=0x1CD8(7384),s0=429336,s1=193061,s0-s1=236275
reg[0x0978]=0x1CD7(7383),reg[0x097A]=0x1CD7(7383),s0=429300,s1=192996,s0-s1=236304
reg[0x0978]=0x1CD7(7383),reg[0x097A]=0x1CD7(7383),s0=429339,s1=192966,s0-s1=236373
reg[0x0978]=0x1CD7(7383),reg[0x097A]=0x1CD7(7383),s0=429262,s1=193005,s0-s1=236257
【电池1电压】通过,【3353】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:19:874】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:19:878】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR】
bat:3354,0,ir:0,cap:0,0,0,gsenor:-190,-59,-912
reg[0x0978]=0x1CD7(7383),reg[0x097A]=0x1CD7(7383),s0=429270,s1=192992,s0-s1=236278
reg[0x0978]=0x1CD7(7383),reg[0x097A]=0x1CD7(7383),s0=429283,s1=192984,s0-s1=236299
U?reg[0x0978]=0x1CD7(7383),reg[0x097A]=0x1CD7(7383),s0=429230,s1=192992,s0-s1=236238
reg[0x0978]=0x1CD7(7383),reg[0x097A]=0x1CD7(7383),s0=429262,s1=192994,s0-s1=236268
reg[0x0978]=0x1CD7(7383),reg[0x097A]=0x1CD7(7383),s0=429251,s1=192934,s0-s1=236317
【电池1电压】通过,【3354】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:27:20:884】开始检测【8.电池2电压】
【电池2电压】失败,返回值【0】与目标值【2900】至【5500】不匹配!
#################### 【测试结束】 ####################
向【COM5】发送指令:【AT+DISCON】
OK+LOST
向【COM5】发送指令:【AT+RESET】
OK+RESET