﻿using Sunny.UI;

namespace GD10Finish.Config
{
    [ConfigFile("Config\\Setting.ini")]
    public class Setting : IniConfig<Setting>
    {
        [ConfigSection("App")] public string UserName { get; set; }
        [ConfigSection("App")] public string StationName { get; set; }
        public string SerialPortDevice { get; set; }
        public int SerialPortDeviceBaudRate { get; set; } = 115200;

        public string SerialPortCurrent { get; set; }
        public int SerialPortCurrentBaudRate { get; set; } = 921600;

        public string SerialPortScan { get; set; }
        public int SerialPortScanBaudRate { get; set; } = 115200;
        public int SnLength { get; set; }


        public override void SetDefault()
        {
            base.SetDefault();
            UserName = "Mes账号";
            StationName = "测试站点";
            SerialPortDevice = "COM1";
            SerialPortCurrent = "COM2";
            SerialPortScan = "COM3";
            SnLength = 28;
        }
    }
}