{"Project": "GD10_Finish", "Version": "2.0.0.0", "UpdateTime": "2025-06-12 10:35:08", "Data": [{"Name": "扫描设备RSSI", "CmdStr": "AT+SCAN?", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "Chars Found:", "WaitTime": 1000, "WaitCount": 6, "Retry": 3, "SleepTime": 0, "Remark": "", "Params": [{"Name": "扫描设备RSSI", "IsEnabled": true, "RegexPattern": "0x[mac], -.*, MHG5", "CutTextType": 3, "CutTextFlag1": ",", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": -70.0, "MaxValue": -10.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "连接蓝牙设备(1)", "CmdStr": "AT+CON[mac]", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "Pairing success", "WaitTime": 1000, "WaitCount": 5, "Retry": 2, "SleepTime": 0, "Remark": "", "Params": [{"Name": "连接蓝牙设备(1)", "IsEnabled": true, "RegexPattern": "Pairing success", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "Pairing success", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭日志打印", "CmdStr": "AT+SETLOG=0", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "log level 0", "WaitTime": 1000, "WaitCount": 1, "Retry": 5, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭日志打印", "IsEnabled": true, "RegexPattern": "log level 0", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "log level 0", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "打开电容读取", "CmdStr": "AT+SETLOG=11", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "log level 0", "WaitTime": 1000, "WaitCount": 1, "Retry": 5, "SleepTime": 0, "Remark": "", "Params": [{"Name": "打开电容读取", "IsEnabled": true, "RegexPattern": "log level 11", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "log level 11", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "校验版本信息", "CmdStr": "AT+INFO", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "mac", "WaitTime": 1000, "WaitCount": 1, "Retry": 5, "SleepTime": 0, "Remark": "", "Params": [{"Name": "校验BootVer", "IsEnabled": true, "RegexPattern": "bootver:.*,", "CutTextType": 3, "CutTextFlag1": ":", "CutTextFlag2": ",", "IsCompareText": true, "TargetValue": "102", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "校验hardver", "IsEnabled": true, "RegexPattern": "hardver:.*,", "CutTextType": 3, "CutTextFlag1": ":", "CutTextFlag2": ",", "IsCompareText": true, "TargetValue": "102", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "校验appver", "IsEnabled": false, "RegexPattern": "appver:.*,", "CutTextType": 3, "CutTextFlag1": ":", "CutTextFlag2": ",", "IsCompareText": true, "TargetValue": "90f", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "未佩戴检测", "CmdStr": "AT+SENSOR", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "g<PERSON>or", "WaitTime": 1000, "WaitCount": 1, "Retry": 5, "SleepTime": 0, "Remark": "", "Params": [{"Name": "电池1电压", "IsEnabled": true, "RegexPattern": "bat.*,", "CutTextType": 3, "CutTextFlag1": "bat:", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 2900.0, "MaxValue": 3500.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "电池2电压", "IsEnabled": true, "RegexPattern": "bat.*,.*,", "CutTextType": 3, "CutTextFlag1": ",", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 2900.0, "MaxValue": 5500.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "红外传感器(未佩戴)", "IsEnabled": true, "RegexPattern": "ir.\\d+,", "CutTextType": 3, "CutTextFlag1": ":", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 0.0, "MaxValue": 0.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "电容传感器(未佩戴)", "IsEnabled": true, "RegexPattern": "cap.\\d+,", "CutTextType": 3, "CutTextFlag1": ":", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": -10000.0, "MaxValue": 10000.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "三轴数据X(未佩戴)", "IsEnabled": true, "RegexPattern": "gsenor.*,", "CutTextType": 3, "CutTextFlag1": "gsenor:", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": -2000.0, "MaxValue": 1800.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "三轴数据Y(未佩戴)", "IsEnabled": true, "RegexPattern": "gsenor.*,.*,", "CutTextType": 3, "CutTextFlag1": ",", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": -2000.0, "MaxValue": 1800.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "三轴数据Z(未佩戴)", "IsEnabled": true, "RegexPattern": "gsenor:[^,]*,([^,]*,([^,]*))", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": -2000.0, "MaxValue": 2000.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "已佩戴检测", "CmdStr": "AT+SENSOR=2", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "g<PERSON>or", "WaitTime": 1000, "WaitCount": 1, "Retry": 5, "SleepTime": 0, "Remark": "", "Params": [{"Name": "红外传感器(已佩戴)", "IsEnabled": true, "RegexPattern": "ir.\\d+,", "CutTextType": 3, "CutTextFlag1": ":", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 300.0, "MaxValue": 3800.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "电容传感器(已佩戴)", "IsEnabled": true, "RegexPattern": "cap.\\d+,", "CutTextType": 3, "CutTextFlag1": ":", "CutTextFlag2": ",", "IsCompareText": true, "TargetValue": "1", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 1.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "三轴数据X(已佩戴)", "IsEnabled": true, "RegexPattern": "gsenor.*,", "CutTextType": 3, "CutTextFlag1": "gsenor:", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": -2000.0, "MaxValue": 1800.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "三轴数据Y(已佩戴)", "IsEnabled": true, "RegexPattern": "gsenor.*,.*,", "CutTextType": 3, "CutTextFlag1": ",", "CutTextFlag2": ",", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": -2000.0, "MaxValue": 1800.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}, {"Name": "三轴数据Z(已佩戴)", "IsEnabled": true, "RegexPattern": "gsenor:[^,]*,([^,]*,([^,]*))", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": false, "TargetValue": "", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": -2000.0, "MaxValue": 2000.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "关闭电容读取", "CmdStr": "AT+SETLOG=10", "AsciiSend": true, "EnterLine": true, "ClearData": true, "EndWithStr": "CAP_NO_TOUCHING,", "WaitTime": 1000, "WaitCount": 8, "Retry": 2, "SleepTime": 0, "Remark": "", "Params": [{"Name": "关闭电容读取", "IsEnabled": true, "RegexPattern": "log level 10", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "log level 10", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 50.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}, {"Name": "MES过站", "CmdStr": "", "AsciiSend": false, "EnterLine": false, "ClearData": true, "EndWithStr": "", "WaitTime": 250, "WaitCount": 4, "Retry": 1, "SleepTime": 0, "Remark": "", "Params": [{"Name": "MES过站", "IsEnabled": false, "RegexPattern": "^[A-Za-z]+", "CutTextType": 0, "CutTextFlag1": "", "CutTextFlag2": "", "IsCompareText": true, "TargetValue": "True", "CompareTextType": 0, "RestoreBase": 1.0, "MinValue": 1.0, "MaxValue": 10.0, "Unit": "", "ShowInfoBefore": "", "ShowInfoAfter": "", "ErrorCode": "", "Remark": ""}]}]}