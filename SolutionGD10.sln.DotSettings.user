﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=E_003A_005CSolutionBsjHelper_005CBsjHelperV2_005Cbin_005CDebug_005CBsjHelperV2_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=F_003A_005CBSJDLL_005CAiHelper_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=F_003A_005CBSJDLL_005CBsjHelperV2_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=F_003A_005CBSJDLL_005CBSJ_002EAI_002EMES_002EDynamicInvocation_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=F_003A_005CBSJDLL_005CControlHelper_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/AddReferences/RecentPaths/=F_003A_005CBSJDLL_005CInterop_002EBarTender_002Edll/@EntryIndexedValue">True</s:Boolean>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003A_005F_005FError_002Ecs_002Fl_003AC_0021_003FUsers_003FAdministrator_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F1a34d7ee85f44ff2a9df5d0ea5a6ee325735b0_003F73_003F27b1ec9f_003F_005F_005FError_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003A_005F_005FError_002Ecs_002Fl_003A_002E_002E_003F_002E_002E_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F1a34d7ee85f44ff2a9df5d0ea5a6ee325735b0_003F73_003F27b1ec9f_003F_005F_005FError_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/Environment/AssemblyExplorer/XmlDocument/@EntryValue">&lt;AssemblyExplorer&gt;&#xD;
  &lt;Assembly Path="E:\SolutionBsjHelper\BsjHelperV2\bin\Debug\BsjHelperV2.dll" /&gt;&#xD;
&lt;/AssemblyExplorer&gt;</s:String></wpf:ResourceDictionary>