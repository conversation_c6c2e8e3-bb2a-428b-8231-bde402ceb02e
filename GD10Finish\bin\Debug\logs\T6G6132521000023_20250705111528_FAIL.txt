2025-07-05 11:15:06:409
扫码结果:T6G6132521000023D93E1635CAF7
开始测试T6G6132521000023
当前测试项目:GD10_Finish
测试参数版本:2.0.0.0
参数更新时间:2025-06-12 10:35:08


▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:06:417】开始检测【1.扫描设备RSSI】
向【COM5】发送指令:【AT+SCAN?】
Scanning...
0: 0x22B66CEF72EA, -62
1: 0x37E7D4B854AB, -63
2: 0xCD091435BEF9, -44, MHG5
Devices Found: 3

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:12:422】开始检测【1.扫描设备RSSI】
向【COM5】发送指令:【AT+SCAN?】
Scanning...
0: 0x37E7D4B854AB, -62
1: 0x22B66CEF72EA, -62
2: 0xD93E1635CAF7, -38, MHG5
3: 0x24F1503DF65B, -63, ROADBIT
4: 0x88231FF6CAD8, -65, HBB8882302301SOC090
5: 0x262A5DE39C9C, -65
Devices Found: 6
【扫描设备RSSI】通过,【-38】符合目标值范围【-70】至【-10】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:18:433】开始检测【2.连接蓝牙设备(1)】
向【COM5】发送指令:【AT+COND93E1635CAF7】
Connecting
Pairing started
Pairing started
OK+CONN:0xD93E1635CAF7
Pairing success
【连接蓝牙设备(1)】通过,返回值【Pairing success】与目标值【Pairing success】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:19:452】开始检测【3.关闭日志打印】
向【COM5】发送指令:【AT+SETLOG=0】
0: 2A00, Read
1: 2A01, Read
2: 2AA6, Read
3: 2AC9, Read
4: 2A05, Indicate
5: A6ED0202-D344-460A-8075-B9E8EC90D71B, Notify; RX
6: A6ED0203-D344-460A-8075-B9E8EC90D71B, Write no rsp, Write; TX
7: A6ED0204-D344-460A-8075-B9E8EC90D71B, Write, Notify
8: A6ED0702-D344-460A-8075-B9E8EC90D71B, Write no rsp, Notify
9: A6ED0703-D344-460A-8075-B9E8EC90D71B, Write no rsp
Chars Found: 10
U?log level 0
【关闭日志打印】通过,返回值【log level 0】与目标值【log level 0】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:20:465】开始检测【4.打开电容读取】
向【COM5】发送指令:【AT+SETLOG=11】
log level 11
reg[0x0978]=0x1D00(7424),reg[0x097A]=0x1D00(7424),s0=429930,s1=192382,s0-s1=237548
U?【打开电容读取】通过,返回值【log level 11】与目标值【log level 11】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:21:474】开始检测【5.校验BootVer】
向【COM5】发送指令:【AT+INFO】
reg[0x0978]=0x1D05(7429),reg[0x097A]=0x1D05(7429),s0=429958,s1=192285,s0-s1=237673
bootver:102,hardver:102,appver:928,mac:D9:3E:16:35:CA:F7reg[0x0978]=0x1CFF(7423),reg[0x097A]=0x1CFF(7423),s0=429696,s1=192164,s0-s1=237532
reg[0x0978]=0x1CFF(7423),reg[0x097A]=0x1CFF(7423),s0=429690,s1=192146,s0-s1=237544
reg[0x0978]=0x1CFF(7423),reg[0x097A]=0x1CFF(7423),s0=429623,s1=192153,s0-s1=237470
reg[0x0978]=0x1CFE(7422),reg[0x097A]=0x1CFE(7422),s0=429604,s1=192107,s0-s1=237497
reg[0x0978]=0x1CFE(7422),reg[0x097A]=0x1CFE(7422),s0=429541,s1=192042,s0-s1=237499
【校验BootVer】通过,返回值【102】与目标值【102】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:22:487】开始检测【6.校验hardver】
【校验hardver】通过,返回值【102】与目标值【102】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:22:503】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
bat:3352,0,ir:1,cap:0,0,gsenor:-307,-32,-905,cap2:237508,429630,192122
reg[0x0978]=0x1CFE(7422),reg[0x097A]=0x1CFE(7422),s0=429484,s1=191977,s0-s1=237507
reg[0x0978]=0x1CFD(7421),reg[0x097A]=0x1CFD(7421),s0=429284,s1=191812,s0-s1=237472
U?reg[0x0978]=0x1CFC(7420),reg[0x097A]=0x1CFC(7420),s0=429108,s1=191710,s0-s1=237398
【电池1电压】通过,【3352】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:23:525】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:23:544】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
bat:3350,0,ir:0,cap:0,0,gsenor:-305,-33,-904,cap2:237474,429404,191929
reg[0x0978]=0x1CFB(7419),reg[0x097A]=0x1CFB(7419),s0=429075,s1=191669,s0-s1=237406
reg[0x0978]=0x1CFB(7419),reg[0x097A]=0x1CFB(7419),s0=429085,s1=191549,s0-s1=237536
reg[0x0978]=0x1CFB(7419),reg[0x097A]=0x1CFB(7419),s0=428978,s1=191601,s0-s1=237377
reg[0x0978]=0x1CFA(7418),reg[0x097A]=0x1CFA(7418),s0=428891,s1=191519,s0-s1=237372
reg[0x0978]=0x1CFA(7418),reg[0x097A]=0x1CFA(7418),s0=428889,s1=191515,s0-s1=237374
【电池1电压】通过,【3350】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:24:567】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:24:582】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
reg[0x0978]=0x1CFA(7418),reg[0x097A]=0x1CFA(7418),s0=428887,s1=191442,s0-s1=237445
bat:3351,0,ir:0,cap:0,0,gsenor:-306,-29,-907,cap2:237420,428946,191525
reg[0x0978]=0x1CFA(7418),reg[0x097A]=0x1CFA(7418),s0=428769,s1=191470,s0-s1=237299
reg[0x0978]=0x1CF9(7417),reg[0x097A]=0x1CF9(7417),s0=428657,s1=191306,s0-s1=237351
reg[0x0978]=0x1CF9(7417),reg[0x097A]=0x1CF9(7417),s0=428571,s1=191231,s0-s1=237340
reg[0x0978]=0x1CF9(7417),reg[0x097A]=0x1CF9(7417),s0=428576,s1=191206,s0-s1=237370
【电池1电压】通过,【3351】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:25:593】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:25:599】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
bat:3353,0,ir:1,cap:0,0,gsenor:-300,-26,-909,cap2:237361,428692,191331
reg[0x0978]=0x1CF9(7417),reg[0x097A]=0x1CF9(7417),s0=428468,s1=191117,s0-s1=237351
reg[0x0978]=0x1CF9(7417),reg[0x097A]=0x1CF9(7417),s0=428355,s1=191035,s0-s1=237320
reg[0x0978]=0x1CF9(7417),reg[0x097A]=0x1CF9(7417),s0=428278,s1=190948,s0-s1=237330
U?reg[0x0978]=0x1CF8(7416),reg[0x097A]=0x1CF8(7416),s0=428196,s1=190948,s0-s1=237248
reg[0x0978]=0x1CF8(7416),reg[0x097A]=0x1CF8(7416),s0=428134,s1=190821,s0-s1=237313
【电池1电压】通过,【3353】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:26:607】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:26:610】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
bat:3351,0,ir:0,cap:0,0,gsenor:-305,-34,-900,cap2:237312,428286,190973
reg[0x0978]=0x1CF8(7416),reg[0x097A]=0x1CF8(7416),s0=428042,s1=190743,s0-s1=237299
reg[0x0978]=0x1CF8(7416),reg[0x097A]=0x1CF8(7416),s0=427911,s1=190623,s0-s1=237288
reg[0x0978]=0x1CF8(7416),reg[0x097A]=0x1CF8(7416),s0=427876,s1=190603,s0-s1=237273
reg[0x0978]=0x1CF7(7415),reg[0x097A]=0x1CF7(7415),s0=427779,s1=190569,s0-s1=237210
【电池1电压】通过,【3351】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:15:27:635】开始检测【8.电池2电压】
【电池2电压】失败,返回值【0】与目标值【2900】至【5500】不匹配!
#################### 【测试结束】 ####################
向【COM5】发送指令:【AT+DISCON】
OK+LOST
向【COM5】发送指令:【AT+RESET】
OK+RESET