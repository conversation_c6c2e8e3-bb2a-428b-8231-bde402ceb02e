2025-07-05 11:13:11:911
扫码结果:T6G6132521000076CD091435BEF9
开始测试T6G6132521000076
当前测试项目:GD10_Finish
测试参数版本:2.0.0.0
参数更新时间:2025-06-12 10:35:08


▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:11:949】开始检测【1.扫描设备RSSI】
向【COM5】发送指令:【AT+SCAN?】
Scanning...
0: 0x37E7D4B854AB, -61
1: 0xCD091435BEF9, -47, MHG5
2: 0x22B66CEF72EA, -63
3: 0xCAEB168F10AB, -64, HB7120059960
4: 0x88231F49CF92, -65, HBB8892015403SOC009
Devices Found: 5
【扫描设备RSSI】通过,【-47】符合目标值范围【-70】至【-10】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:18:121】开始检测【2.连接蓝牙设备(1)】
向【COM5】发送指令:【AT+CONCD091435BEF9】
Connecting
Pairing started
Pairing started
OK+CONN:0xCD091435BEF9
Pairing success
【连接蓝牙设备(1)】通过,返回值【Pairing success】与目标值【Pairing success】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:19:164】开始检测【3.关闭日志打印】
向【COM5】发送指令:【AT+SETLOG=0】
0: 2A00, Read
1: 2A01, Read
2: 2AA6, Read
3: 2AC9, Read
4: 2A05, Indicate
5: A6ED0202-D344-460A-8075-B9E8EC90D71B, Notify; RX
6: A6ED0203-D344-460A-8075-B9E8EC90D71B, Write no rsp, Write; TX
7: A6ED0204-D344-460A-8075-B9E8EC90D71B, Write, Notify
8: A6ED0702-D344-460A-8075-B9E8EC90D71B, Write no rsp, Notify
9: A6ED0703-D344-460A-8075-B9E8EC90D71B, Write no rsp
Chars Found: 10
U?log level 0
【关闭日志打印】通过,返回值【log level 0】与目标值【log level 0】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:20:182】开始检测【4.打开电容读取】
向【COM5】发送指令:【AT+SETLOG=11】
log level 11
U?reg[0x0978]=0x1BE5(7141),reg[0x097A]=0x1BE5(7141),s0=406029,s1=177527,s0-s1=228502
reg[0x0978]=0x1C1B(7195),reg[0x097A]=0x1C1B(7195),s0=407899,s1=177634,s0-s1=230265
reg[0x0978]=0x1D33(7475),reg[0x097A]=0x1D33(7475),s0=419903,s1=178053,s0-s1=241850
【打开电容读取】通过,返回值【log level 11】与目标值【log level 11】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:21:189】开始检测【5.校验BootVer】
向【COM5】发送指令:【AT+INFO】
reg[0x0978]=0x1D33(7475),reg[0x097A]=0x1D33(7475),s0=439241,s1=180226,s0-s1=259015
bootver:102,hardver:102,appver:928,mac:CD:09:14:35:BE:F9reg[0x0978]=0x1D33(7475),reg[0x097A]=0x1D35(7477),s0=372244,s1=173316,s0-s1=198928
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1BAA(7082),s0=370381,s1=171841,s0-s1=198540
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1BAA(7082),s0=417407,s1=169718,s0-s1=247689
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1BD7(7127),s0=495850,s1=172667,s0-s1=323183
【校验BootVer】通过,返回值【102】与目标值【102】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:22:198】开始检测【6.校验hardver】
【校验hardver】通过,返回值【102】与目标值【102】完全一致。

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:22:213】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
bat:3335,0,ir:95,cap:0,0,gsenor:-219,-5,929,cap2:245471,419024,173553
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1BEA(7146),s0=328739,s1=132114,s0-s1=196625
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1BF0(7152),s0=367999,s1=133053,s0-s1=234946
U?reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1BF5(7157),s0=383691,s1=130913,s0-s1=252778
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1C0C(7180),s0=433691,s1=132124,s0-s1=301567
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1C35(7221),s0=402852,s1=131094,s0-s1=271758
【电池1电压】通过,【3335】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:23:248】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:23:314】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
bat:3335,0,ir:95,cap:0,0,gsenor:-219,-5,929,cap2:251534,383394,131859
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1C5C(7260),s0=384921,s1=129253,s0-s1=255668
reg[0x0978]=0x1BA9(7081),reg[0x097A]=0x1C72(7282),s0=339151,s1=131717,s0-s1=207434
reg[0x0978]=0x1AED(6893),reg[0x097A]=0x1B78(7032),s0=326700,s1=131748,s0-s1=194952
reg[0x0978]=0x181E(6174),reg[0x097A]=0x1984(6532),s0=325191,s1=131046,s0-s1=194145
【电池1电压】通过,【3335】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:24:354】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:24:376】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
reg[0x0978]=0x17FC(6140),reg[0x097A]=0x18F2(6386),s0=325707,s1=131681,s0-s1=194026
bat:3335,0,ir:95,cap:0,0,gsenor:-219,-5,929,cap2:209245,340334,131089
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x188E(6286),s0=325647,s1=131499,s0-s1=194148
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x188E(6286),s0=333522,s1=131842,s0-s1=201680
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x188E(6286),s0=335923,s1=132253,s0-s1=203670
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x1890(6288),s0=336161,s1=132377,s0-s1=203784
【电池1电压】通过,【3335】符合目标值范围【2900】至【3500】
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x1892(6290),s0=338573,s1=133348,s0-s1=205225

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:25:386】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:25:390】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
bat:3335,0,ir:95,cap:0,0,gsenor:-219,-5,929,cap2:201701,333965,132263
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x1895(6293),s0=340402,s1=133310,s0-s1=207092
U?reg[0x0978]=0x17B2(6066),reg[0x097A]=0x189A(6298),s0=339900,s1=133181,s0-s1=206719
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x18A9(6313),s0=346698,s1=133955,s0-s1=212743
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x18B3(6323),s0=349490,s1=134259,s0-s1=215231
【电池1电压】通过,【3335】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:26:400】开始检测【8.电池2电压】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:26:405】开始检测【7.电池1电压】
向【COM5】发送指令:【AT+SENSOR=2】
bat:3335,0,ir:95,cap:0,0,gsenor:-219,-5,929,cap2:209402,343012,133610
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x18BF(6335),s0=352434,s1=134910,s0-s1=217524
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x18CC(6348),s0=353211,s1=136226,s0-s1=216985
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x18D9(6361),s0=349917,s1=135141,s0-s1=214776
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x18E6(6374),s0=356122,s1=138369,s0-s1=217753
reg[0x0978]=0x17B2(6066),reg[0x097A]=0x18F2(6386),s0=357477,s1=140513,s0-s1=216964
【电池1电压】通过,【3335】符合目标值范围【2900】至【3500】

▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼
【2025-07-05 11:13:27:419】开始检测【8.电池2电压】
【电池2电压】失败,返回值【0】与目标值【2900】至【5500】不匹配!
#################### 【测试结束】 ####################
向【COM5】发送指令:【AT+DISCON】
OK+LOST
向【COM5】发送指令:【AT+RESET】
OK+RESET